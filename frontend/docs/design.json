{"designSystem": {"name": "Trodoo Vibe Design System", "version": "2.0.0", "description": "A comprehensive design system for the Trodoo venue rental platform. Features glassmorphism effects, gradient overlays, professional green/amber palette, and modern interactive components with smooth animations."}, "brandPersonality": {"tone": "professional, trustworthy, modern, efficient, premium", "visualStyle": "glassmorphism, gradient overlays, clean, vibrant, sophisticated", "userExperience": "seamless, confident, intuitive, delightful, responsive", "targetAudience": "business professionals, event organizers, venue owners, quality-conscious consumers"}, "colorPalette": {"primary": {"50": "#ECFDF5", "100": "#D1FAE5", "200": "#A7F3D0", "300": "#6EE7B7", "400": "#34D399", "500": "#10B981", "600": "#059669", "700": "#047857", "800": "#065F46", "900": "#064E3B", "green": "#059669", "greenLight": "#10B981", "greenDark": "#047857", "greenSubtle": "#D1FAE5", "greenMuted": "#6EE7B7"}, "secondary": {"50": "#FFFBEB", "100": "#FEF3C7", "200": "#FDE68A", "300": "#FCD34D", "400": "#FBBF24", "500": "#F59E0B", "600": "#D97706", "700": "#B45309", "800": "#92400E", "900": "#78350F", "yellow": "#F59E0B", "yellowLight": "#FBBF24", "yellowDark": "#D97706", "yellowSubtle": "#FEF3C7", "yellowMuted": "#FCD34D"}, "neutral": {"white": "#FFFFFF", "lightGray": "#F9FAFB", "mediumGray": "#E5E7EB", "darkGray": "#6B7280", "charcoal": "#374151", "black": "#1F2937"}, "slate": {"50": "#F8FAFC", "100": "#F1F5F9", "200": "#E2E8F0", "300": "#CBD5E1", "400": "#94A3B8", "500": "#64748B", "600": "#475569", "700": "#334155", "800": "#1E293B", "900": "#0F172A"}, "gradients": {"primary": "linear-gradient(135deg, #059669 0%, #10B981 100%)", "secondary": "linear-gradient(135deg, #F59E0B 0%, #FBBF24 100%)", "hero": "linear-gradient(135deg, rgba(5, 150, 105, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%)", "glassmorphism": "rgba(255, 255, 255, 0.2)", "glassmorphismDark": "rgba(0, 0, 0, 0.1)", "feature": "linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%)"}}, "typography": {"fontFamily": {"primary": "'<PERSON>pin<PERSON>', 'Helvetica Neue', <PERSON><PERSON>, sans-serif", "body": "'Robot<PERSON>', 'Helvetica Neue', <PERSON><PERSON>, sans-serif", "display": "'<PERSON>', sans-serif", "interface": "'Inter', sans-serif", "fallback": "system-ui, -apple-system, sans-serif"}, "fontWeights": {"regular": 400, "medium": 500, "semibold": 600, "bold": 700, "extrabold": 800}, "headings": {"hero": {"fontSize": "6rem", "fontWeight": 800, "lineHeight": 1.1, "color": "neutral.white", "fontFamily": "display", "usage": "Hero headlines with gradient text"}, "h1": {"fontSize": "3rem", "fontWeight": 700, "lineHeight": 1.2, "color": "slate.900", "usage": "Page titles"}, "h2": {"fontSize": "2.25rem", "fontWeight": 700, "lineHeight": 1.3, "color": "slate.900", "usage": "Section headers"}, "h3": {"fontSize": "1.5rem", "fontWeight": 600, "lineHeight": 1.4, "color": "slate.900", "usage": "Card titles, feature headers"}}, "body": {"large": {"fontSize": "1.25rem", "fontWeight": 400, "lineHeight": 1.6, "color": "slate.600", "usage": "Hero descriptions, large body text"}, "regular": {"fontSize": "1rem", "fontWeight": 400, "lineHeight": 1.5, "color": "slate.600", "usage": "General body text, feature descriptions"}, "small": {"fontSize": "0.875rem", "fontWeight": 500, "lineHeight": 1.4, "color": "slate.700", "usage": "Form labels, secondary text"}}}, "spacing": {"scale": "8px base unit", "values": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem", "3xl": "4rem", "4xl": "5rem", "5xl": "6rem"}}, "borderRadius": {"sm": "0.25rem", "md": "0.5rem", "lg": "0.75rem", "xl": "1.25rem", "2xl": "1rem", "full": "9999px"}, "shadows": {"subtle": "0 2px 4px rgba(0, 0, 0, 0.05)", "medium": "0 4px 8px rgba(0, 0, 0, 0.1)", "large": "0 10px 20px rgba(0, 0, 0, 0.15)", "card": "0 8px 16px rgba(0,0,0,0.1)", "glassmorphism": "0 8px 32px rgba(0, 0, 0, 0.1)", "glow": "0 0 20px rgba(5, 150, 105, 0.3)"}, "layout": {"containerWidth": "1280px", "maxWidth": "7xl", "header": {"structure": "Floating dock navigation at bottom", "background": "glassmorphism with backdrop blur", "height": "auto", "shadow": "glassmorphism", "position": "fixed bottom"}, "hero": {"structure": "Full-screen with GridMotion background and overlay", "background": "GridMotion with gradient overlay", "borderRadius": "none", "padding": "5xl 2xl", "overlay": "gradients.hero"}, "features": {"structure": "Three-column grid with SpotlightTiltCard", "background": "gradient from slate-50 to white", "padding": "5xl 2xl", "decorativeElements": "floating gradient orbs"}}, "components": {"buttons": {"primary": {"background": "secondary.500", "color": "neutral.black", "padding": "0.75rem 1.5rem", "borderRadius": "full", "fontWeight": 700, "fontSize": "1rem", "shadow": "medium", "hoverState": {"background": "secondary.600", "transform": "scale(1.02)", "shadow": "large"}}, "secondary": {"background": "transparent", "color": "neutral.white", "border": "2px solid neutral.white", "padding": "0.625rem 1.375rem", "borderRadius": "full", "fontWeight": 600, "hoverState": {"background": "neutral.white", "color": "primary.600", "transform": "scale(1.02)"}}, "glassmorphism": {"background": "rgba(255, 255, 255, 0.2)", "backdropFilter": "blur(12px)", "color": "neutral.white", "border": "1px solid rgba(255, 255, 255, 0.3)", "padding": "0.75rem 1.5rem", "borderRadius": "xl", "hoverState": {"background": "rgba(255, 255, 255, 0.3)"}}}, "cards": {"spotlight": {"background": "neutral.white", "borderRadius": "xl", "shadow": "card", "padding": "2rem", "border": "1px solid slate.200", "hoverState": {"shadow": "large", "transform": "translateY(-2px)"}}, "glassmorphism": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(16px)", "borderRadius": "2xl", "border": "1px solid rgba(255, 255, 255, 0.2)", "shadow": "glassmorphism"}}, "forms": {"fields": {"input": {"borderRadius": "lg", "border": "1px solid slate.300", "padding": "0.75rem 1rem", "fontSize": "1rem", "backgroundColor": "neutral.white", "fontFamily": "body", "focusState": {"borderColor": "primary.600", "boxShadow": "0 0 0 3px rgba(5, 150, 105, 0.1)", "outline": "none"}, "errorState": {"borderColor": "red.500", "boxShadow": "0 0 0 3px rgba(239, 68, 68, 0.1)"}}, "glassmorphismInput": {"background": "transparent", "border": "none", "color": "neutral.white", "placeholder": "rgba(255, 255, 255, 0.8)", "padding": "0.75rem 1rem", "fontSize": "1rem", "focusState": {"outline": "none", "ring": "none"}}, "select": {"appearance": "none", "backgroundImage": "chevron-down icon", "paddingRight": "2.5rem"}}, "labels": {"fontSize": "0.875rem", "fontWeight": 500, "color": "slate.700", "marginBottom": "0.5rem"}}, "navigation": {"dock": {"style": "floating glassmorphism dock", "background": "rgba(255, 255, 255, 0.2)", "backdropFilter": "blur(16px)", "border": "1px solid rgba(255, 255, 255, 0.3)", "borderRadius": "2xl", "padding": "1rem", "shadow": "glassmorphism", "items": {"size": "4rem", "borderRadius": "xl", "spacing": "0.5rem", "defaultState": {"color": "slate.600", "background": "transparent"}, "hoverState": {"scale": "1.1", "translateY": "-2px", "color": "slate.900", "background": "slate.100"}, "activeState": {"background": "primary.100", "color": "primary.700"}, "highlightState": {"background": "linear-gradient(to right, primary.500, primary.600)", "color": "white", "shadow": "glow"}}}}, "icons": {"style": "lucide line icons", "strokeWidth": "2px", "size": {"small": "1rem", "regular": "1.5rem", "large": "2rem", "feature": "2.5rem"}, "color": "inherit"}}, "effects": {"glassmorphism": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(16px)", "border": "1px solid rgba(255, 255, 255, 0.2)"}, "gridMotion": {"gradientColor": "rgba(5, 150, 105, 0.1)", "items": "venue types and event categories"}, "spotlightTilt": {"spotlightColor": "rgba(16, 185, 129, 0.3)", "tiltEffect": "3D perspective on hover"}}, "animations": {"transitions": {"duration": "300ms", "easing": "cubic-bezier(0.4, 0, 0.2, 1)", "properties": ["all"]}, "hoverEffects": {"buttons": "scale(1.02) with shadow increase", "cards": "translateY(-2px) with shadow increase", "dock": "scale(1.1) translateY(-2px) with rotation"}, "stagger": {"children": "0.1s delay between items", "duration": "0.6s"}}, "responsiveDesign": {"breakpoints": {"sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px"}, "heroAdaptation": {"mobile": "Full-screen with stacked search form", "tablet": "Maintain full-screen with responsive search", "desktop": "Full-screen with centered content"}, "featureGrid": {"mobile": "Single column", "tablet": "Two columns", "desktop": "Three columns"}, "dockAdaptation": {"mobile": "Hide on keyboard focus", "tablet": "Always visible", "desktop": "Always visible with hover effects"}}}
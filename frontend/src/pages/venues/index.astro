---
import Layout from '../../components/core/Layout.astro';
import VenueSearch from '../../components/venues/VenueSearch.tsx';
---

<Layout
  title="Browse Venues - Trodoo"
  description="Discover unique venues for your special events. From intimate gatherings to grand celebrations."
>
  <div class="min-h-screen relative overflow-hidden" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
    <!-- Background Grid Pattern -->
    <div class="absolute inset-0 opacity-30 pointer-events-none">
      <div
        class="absolute inset-0"
        style="background-image: radial-gradient(circle at 1px 1px, rgba(5, 150, 105, 0.1) 1px, transparent 0); background-size: 40px 40px;"
      ></div>
    </div>


    <!-- Search Section -->
    <div id="search-section" class="relative bg-gradient-to-br from-slate-50 to-white">
      <!-- Floating Gradient Orbs for Search Section -->
      <div class="absolute top-20 right-20 w-48 h-48 opacity-10" style="background: linear-gradient(135deg, rgba(5, 150, 105, 0.3) 0%, rgba(16, 185, 129, 0.2) 100%); border-radius: 50%; filter: blur(40px);"></div>
      <div class="absolute bottom-20 left-20 w-64 h-64 opacity-10" style="background: linear-gradient(135deg, rgba(245, 158, 11, 0.3) 0%, rgba(251, 191, 36, 0.2) 100%); border-radius: 50%; filter: blur(50px);"></div>

      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
        <!-- Section Header -->
        <div class="text-center mb-12">
          <h2 
            class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight"
            style="color: #0F172A; font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;"
          >
            Search & Discover
          </h2>
          <p 
            class="text-lg md:text-xl max-w-2xl mx-auto leading-relaxed"
            style="color: #64748B; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
          >
            Find venues that match your exact requirements
          </p>
        </div>

        <!-- Enhanced Search Container -->
        <div 
          class="bg-white rounded-3xl p-6 lg:p-8 border transition-all duration-300 hover:shadow-2xl hover:-translate-y-1"
          style="border-color: #E2E8F0; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);"
        >
          <VenueSearch
            client:load
            onVenueSelect={(venue) => {
              window.location.href = `/venues/${venue.id}`;
            }}
          />
        </div>

        <!-- Popular Categories -->
        <div class="mt-16">
          <h3 
            class="text-2xl md:text-3xl font-bold text-center mb-8"
            style="color: #0F172A; font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;"
          >
            Popular Categories
          </h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 lg:gap-6">
            <div class="group">
              <div 
                class="bg-white rounded-2xl p-6 border transition-all duration-300 hover:scale-105 hover:-translate-y-2 hover:shadow-xl cursor-pointer"
                style="border-color: #E2E8F0; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);"
              >
                <div class="text-center">
                  <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300" style="background: linear-gradient(135deg, #D1FAE5 0%, #A7F3D0 100%);">
                    <svg class="w-6 h-6" style="color: #059669;" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                  </div>
                  <div class="text-sm font-semibold" style="color: #334155; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">Weddings</div>
                </div>
              </div>
            </div>

            <div class="group">
              <div 
                class="bg-white rounded-2xl p-6 border transition-all duration-300 hover:scale-105 hover:-translate-y-2 hover:shadow-xl cursor-pointer"
                style="border-color: #E2E8F0; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);"
              >
                <div class="text-center">
                  <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300" style="background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);">
                    <svg class="w-6 h-6" style="color: #D97706;" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                    </svg>
                  </div>
                  <div class="text-sm font-semibold" style="color: #334155; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">Corporate</div>
                </div>
              </div>
            </div>

            <div class="group">
              <div 
                class="bg-white rounded-2xl p-6 border transition-all duration-300 hover:scale-105 hover:-translate-y-2 hover:shadow-xl cursor-pointer"
                style="border-color: #E2E8F0; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);"
              >
                <div class="text-center">
                  <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300" style="background: linear-gradient(135deg, #D1FAE5 0%, #A7F3D0 100%);">
                    <svg class="w-6 h-6" style="color: #059669;" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                  </div>
                  <div class="text-sm font-semibold" style="color: #334155; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">Parties</div>
                </div>
              </div>
            </div>

            <div class="group">
              <div 
                class="bg-white rounded-2xl p-6 border transition-all duration-300 hover:scale-105 hover:-translate-y-2 hover:shadow-xl cursor-pointer"
                style="border-color: #E2E8F0; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);"
              >
                <div class="text-center">
                  <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300" style="background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);">
                    <svg class="w-6 h-6" style="color: #D97706;" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                  </div>
                  <div class="text-sm font-semibold" style="color: #334155; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">Conferences</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<style>
  /* Custom animations */
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out;
  }

  .animate-fade-in-up-delay {
    animation: fade-in-up 0.8s ease-out 0.2s both;
  }

  .animate-fade-in-up-delay-2 {
    animation: fade-in-up 0.8s ease-out 0.4s both;
  }

  .animate-fade-in-up-delay-3 {
    animation: fade-in-up 0.8s ease-out 0.6s both;
  }

  /* Enhanced hover effects */
  .group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
  }

  .group:hover .group-hover\:text-yellow-300 {
    color: #FCD34D;
  }

  /* Button hover effects - exclude HeaderDock buttons */
  button:not([class*="HeaderDock"]):not([class*="dock"]):hover {
    background-color: #D97706 !important;
    transform: scale(1.05) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  }

  button:not([class*="HeaderDock"]):not([class*="dock"]):active {
    transform: scale(0.98) !important;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Glassmorphism enhancements */
  .backdrop-blur-2xl {
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
  }

  /* Card hover effects */
  .hover\:shadow-2xl:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .hover\:-translate-y-1:hover {
    transform: translateY(-0.25rem);
  }

  .hover\:-translate-y-2:hover {
    transform: translateY(-0.5rem);
  }

  .hover\:scale-105:hover {
    transform: scale(1.05);
  }
</style>

<script>
  // Handle venue navigation and enhanced interactions
  document.addEventListener('DOMContentLoaded', () => {
    console.log('Enhanced venue search page loaded');

    // Add smooth scroll behavior for CTA button
    const ctaButton = document.querySelector('button[onclick*="scrollIntoView"]');
    if (ctaButton) {
      ctaButton.addEventListener('click', (e) => {
        e.preventDefault();
        const searchSection = document.getElementById('search-section');
        if (searchSection) {
          searchSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    }

    // Add category click handlers
    const categoryCards = document.querySelectorAll('.group .cursor-pointer');
    categoryCards.forEach((card, index) => {
      card.addEventListener('click', () => {
        const categories = ['weddings', 'corporate', 'parties', 'conferences'];
        const category = categories[index];
        console.log(`Clicked category: ${category}`);
        // In a real app, this would filter the search results
        // For now, just scroll to search section
        document.getElementById('search-section')?.scrollIntoView({ 
          behavior: 'smooth' 
        });
      });
    });

    // Add intersection observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-fade-in-up');
        }
      });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.group, h2, h3');
    animateElements.forEach(el => observer.observe(el));
  });
</script>

---
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { getVenue } from '../../../lib/pocketbase.ts';

export async function getStaticPaths() {
  // For SSR, we return an empty array since paths will be generated on-demand
  return [];
}

const { id } = Astro.params;

if (!id) {
  return Astro.redirect('/dashboard/my-venues');
}

// Check if user is authenticated
const authCookie = Astro.cookies.get('pb_auth');
if (!authCookie) {
  return Astro.redirect('/auth/login');
}

let venue: any = null;
let error: string | null = null;
let currentUserId: string | null = null;

try {
  // Parse auth cookie to get current user ID
  const authData = JSON.parse(authCookie.value);
  currentUserId = authData.model?.id;
} catch (err) {
  console.error('Failed to parse auth cookie:', err);
  return Astro.redirect('/auth/login');
}

try {
  const result = await getVenue(id, 'owner');
  if (result.success && result.venue) {
    venue = result.venue;

    // Check if current user is the owner of this venue
    // Handle both cases: when owner is expanded (object) and when it's just an ID (string)
    const venueOwnerId = typeof venue.owner === 'object' ? venue.owner.id : venue.owner;

    console.log('Venue owner ID:', venueOwnerId, 'Current user ID:', currentUserId);

    if (venueOwnerId !== currentUserId) {
      console.error('User is not the owner of this venue. Owner:', venueOwnerId, 'User:', currentUserId);
      return Astro.redirect('/dashboard/my-venues');
    }

    console.log('Ownership verified - user can edit this venue');
  } else {
    error = result.error || 'Venue not found';
  }
} catch (err) {
  console.error('Failed to fetch venue:', err);
  error = 'Failed to load venue';
}

if (error || !venue) {
  return Astro.redirect('/dashboard/my-venues');
}

// Transform venue data to match VenueFormData interface
// For editing, we need to handle existing files differently
const venueFormData = {
  title: venue.title,
  description: venue.description,
  address: venue.address,
  capacity: venue.capacity,
  price_per_hour: venue.price_per_hour,
  amenities: venue.amenities || [],
  standard_photos: [], // Start with empty array for new uploads
  pano_photo: undefined, // Start with undefined for new uploads
  rental_agreement_pdf: undefined, // Start with undefined for new uploads
  is_published: venue.is_published,
  move_out_checklist: venue.move_out_checklist || []
};

// Store existing files separately for display
const existingFiles = {
  standard_photos: venue.standard_photos || [],
  pano_photo: venue.pano_photo,
  rental_agreement_pdf: venue.rental_agreement_pdf
};
---

<BaseLayout
  title={`Edit ${venue.title} - Trodoo`}
  description="Edit your venue listing on Trodoo."
>
  <!-- Venue data for client-side JavaScript -->
  <meta name="venue-id" content={venue.id} slot="head" />
  <meta name="venue-data" content={JSON.stringify(venueFormData)} slot="head" />
  <meta name="existing-files" content={JSON.stringify(existingFiles)} slot="head" />
  
  <div class="min-h-screen bg-slate-50">
    <!-- Header Section -->
    <div class="bg-white border-b border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-8">
          <nav class="flex items-center space-x-2 text-sm text-slate-500 mb-4">
            <a href="/dashboard" class="hover:text-slate-700 transition-colors">Dashboard</a>
            <span>/</span>
            <a href="/dashboard/my-venues" class="hover:text-slate-700 transition-colors">My Venues</a>
            <span>/</span>
            <span class="text-slate-900 font-medium">Edit Venue</span>
          </nav>
          <h1 class="text-3xl font-bold text-slate-900">
            Edit Venue
          </h1>
          <p class="mt-2 text-slate-600">
            Update your venue information and settings.
          </p>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div id="venue-form-container">
        <!-- VenueForm component will be mounted here -->
        <div class="text-center py-8 text-slate-500">
          Loading venue form...
        </div>
      </div>
    </div>
  </div>
</BaseLayout>

<script>
  import { updateVenue } from '../../../lib/pocketbase.ts';

  document.addEventListener('DOMContentLoaded', () => {
    console.log('Venue edit page loaded');

    // Get venue data from meta tags
    const venueId = document.querySelector('meta[name="venue-id"]')?.getAttribute('content');
    const venueDataMeta = document.querySelector('meta[name="venue-data"]')?.getAttribute('content');
    const existingFilesMeta = document.querySelector('meta[name="existing-files"]')?.getAttribute('content');

    if (!venueId || !venueDataMeta) {
      console.error('Missing venue data');
      window.location.href = '/dashboard/my-venues';
      return;
    }

    let venueData, existingFiles;
    try {
      venueData = JSON.parse(venueDataMeta);
      existingFiles = existingFilesMeta ? JSON.parse(existingFilesMeta) : {};
    } catch (error) {
      console.error('Failed to parse venue data:', error);
      window.location.href = '/dashboard/my-venues';
      return;
    }

    // For now, let's create a simple edit form instead of using VenueForm
    // which is designed for creating new venues with file uploads
    const container = document.getElementById('venue-form-container');
    if (container) {
      container.innerHTML = `
        <div class="bg-white rounded-xl shadow-lg p-8">
          <h2 class="text-2xl font-bold text-slate-900 mb-6">Edit Venue Details</h2>

          <form id="edit-venue-form" class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-slate-700 mb-2">Title</label>
              <input type="text" id="title" value="${venueData.title}"
                     class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
            </div>

            <div>
              <label class="block text-sm font-medium text-slate-700 mb-2">Description</label>
              <textarea id="description" rows="4"
                        class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">${venueData.description}</textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">Capacity</label>
                <input type="number" id="capacity" value="${venueData.capacity}" min="1"
                       class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
              </div>

              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">Price per Hour ($)</label>
                <input type="number" id="price_per_hour" value="${venueData.price_per_hour}" min="0" step="0.01"
                       class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-slate-700 mb-2">Street Address</label>
              <input type="text" id="street" value="${venueData.address.street}"
                     class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">City</label>
                <input type="text" id="city" value="${venueData.address.city}"
                       class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
              </div>

              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">Postal Code</label>
                <input type="text" id="postal_code" value="${venueData.address.postal_code || ''}"
                       class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
              </div>
            </div>

            <div>
              <label class="flex items-center">
                <input type="checkbox" id="is_published" ${venueData.is_published ? 'checked' : ''}
                       class="rounded border-slate-300 text-emerald-600 focus:ring-emerald-500">
                <span class="ml-2 text-sm text-slate-700">Publish venue (make it visible to renters)</span>
              </label>
            </div>

            ${existingFiles.standard_photos && existingFiles.standard_photos.length > 0 ? `
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">Current Photos</label>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                  ${existingFiles.standard_photos.map((photo: string) => `
                    <img src="${photo}" alt="Venue photo" class="w-full h-24 object-cover rounded-lg border">
                  `).join('')}
                </div>
                <p class="text-xs text-slate-500 mt-2">To change photos, please contact support or create a new venue listing.</p>
              </div>
            ` : ''}

            <div class="flex gap-4 pt-6 border-t">
              <button type="button" id="cancel-btn"
                      class="px-6 py-3 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors">
                Cancel
              </button>
              <button type="submit" id="save-btn"
                      class="px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors">
                Save Changes
              </button>
            </div>
          </form>
        </div>
      `;

      // Add event listeners
      const cancelBtn = document.getElementById('cancel-btn');
      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
          window.location.href = '/dashboard/my-venues';
        });
      }

      const editForm = document.getElementById('edit-venue-form');
      if (editForm) {
        editForm.addEventListener('submit', async (e) => {
          e.preventDefault();

          const saveBtn = document.getElementById('save-btn');
          if (saveBtn) {
            (saveBtn as any).textContent = 'Saving...';
            (saveBtn as any).disabled = true;
          }

          try {
            // Get form elements and validate they exist
            const titleEl = document.getElementById('title');
            const descriptionEl = document.getElementById('description');
            const capacityEl = document.getElementById('capacity');
            const priceEl = document.getElementById('price_per_hour');
            const streetEl = document.getElementById('street');
            const cityEl = document.getElementById('city');
            const postalCodeEl = document.getElementById('postal_code');
            const isPublishedEl = document.getElementById('is_published');

            // Validate all elements exist and have the expected properties
            if (!titleEl || !descriptionEl || !capacityEl || !priceEl ||
                !streetEl || !cityEl || !postalCodeEl || !isPublishedEl) {
              throw new Error('Form elements not found');
            }

            // Type-safe property access
            const formData = {
              title: (titleEl as any).value,
              description: (descriptionEl as any).value,
              capacity: parseInt((capacityEl as any).value),
              price_per_hour: parseFloat((priceEl as any).value),
              address: {
                street: (streetEl as any).value,
                city: (cityEl as any).value,
                country: venueData.address.country, // Keep existing country
                state: venueData.address.state, // Keep existing state
                postal_code: (postalCodeEl as any).value
              },
              is_published: (isPublishedEl as any).checked,
              amenities: venueData.amenities, // Keep existing amenities for now
              move_out_checklist: venueData.move_out_checklist // Keep existing checklist
            };

            const result = await updateVenue(venueId, formData);

            if (result.success) {
              alert('Venue updated successfully! Redirecting to your venues...');
              window.location.href = '/dashboard/my-venues';
            } else {
              alert(`Failed to update venue: ${result.error}`);
            }
          } catch (error) {
            console.error('Error updating venue:', error);
            alert('An unexpected error occurred. Please try again.');
          } finally {
            if (saveBtn) {
              (saveBtn as any).textContent = 'Save Changes';
              (saveBtn as any).disabled = false;
            }
          }
        });
      }
    }
  });
</script>

export interface VenueAddress {
  street: string
  city: string
  country: string
  state?: string
  postal_code?: string
}

export interface ChecklistItem {
  id: string
  text: string
  completed: boolean
}

export interface VenueFormData {
  title: string
  description: string
  address: VenueAddress
  capacity: number
  price_per_hour: number
  amenities: string[]
  standard_photos: File[]
  pano_photo?: File
  rental_agreement_pdf?: File
  is_published: boolean
  move_out_checklist: ChecklistItem[]
}

export interface VenueValidationErrors {
  title?: string
  description?: string
  address?: string
  capacity?: string
  price_per_hour?: string
  pano_photo?: string
  general?: string
}

// Main Venue interface for displaying venue data
export interface Venue {
  id: string
  title: string
  description: string
  address: VenueAddress
  capacity: number
  price_per_hour: number
  amenities: string[]
  standard_photos: string[]
  pano_photo?: string
  rental_agreement_pdf?: string
  is_published: boolean
  owner: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  average_rating?: number
  review_count?: number
  total_bookings?: number
  move_out_checklist?: ChecklistItem[]
  created?: string
  updated?: string
}

// Search filters interface
export interface VenueSearchFilters {
  query?: string
  minPrice?: number
  maxPrice?: number
  minCapacity?: number
  amenities?: string[]
  sort?: 'relevance' | 'price_asc' | 'price_desc' | 'capacity_asc' | 'capacity_desc' | 'rating' | 'newest'
  limit?: number
  page?: number
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export const validatePanoramaFile = async (file: File): Promise<ValidationResult> => {
  const errors: string[] = []
  const warnings: string[] = []

  // Check file type
  if (!file.type.startsWith("image/")) {
    errors.push("File must be an image")
  }

  // Check file size (max 50MB)
  if (file.size > 50 * 1024 * 1024) {
    errors.push("File size must be less than 50MB")
  }

  // For a real implementation, you would check image dimensions
  // This is a simplified version
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  }
}

export const getRecommendedSpecs = (): string => {
  return `Recommended 360° Photo Specifications:
• Format: Equirectangular projection
• Aspect Ratio: 2:1 (width:height)
• Resolution: 4096×2048 to 8192×4096 pixels
• File Format: JPEG or PNG
• File Size: Under 50MB
• Quality: High resolution for best viewing experience`
}

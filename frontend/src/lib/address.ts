// Address formatting utilities

export interface Address {
  street: string;
  city: string;
  state?: string;
  country: string;
}

/**
 * Formats an address object or string into a readable string
 * @param address - Address object or string
 * @returns Formatted address string
 */
export function formatAddress(address: Address | string | null | undefined): string {
  if (typeof address === 'string') return address;
  if (!address) return 'Address not available';
  
  const parts = [address.street, address.city];
  if (address.state) parts.push(address.state);
  parts.push(address.country);
  
  return parts.filter(Boolean).join(', ');
}

/**
 * Formats an address for display in a compact format (city, country)
 * @param address - Address object or string
 * @returns Compact formatted address string
 */
export function formatAddressCompact(address: Address | string | null | undefined): string {
  if (typeof address === 'string') return address;
  if (!address) return 'Address not available';
  
  const parts = [address.city, address.country];
  return parts.filter(Boolean).join(', ');
}

/**
 * Validates if an address object has required fields
 * @param address - Address object to validate
 * @returns True if address is valid
 */
export function isValidAddress(address: any): address is Address {
  return (
    address &&
    typeof address === 'object' &&
    typeof address.street === 'string' &&
    typeof address.city === 'string' &&
    typeof address.country === 'string'
  );
}

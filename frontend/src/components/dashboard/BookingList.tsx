import { useState } from 'react';
import { Calendar, MapPin, Users, DollarSign, MessageSquare, CreditCard, CheckCircle, XCircle } from 'lucide-react';
import type { Booking } from '../../types/booking.ts';
import { CompactStarRating } from '../common/StarRating.tsx';
import Button from '../common/Button.tsx';
import { formatAmount } from '../../lib/paystack.ts';
import { formatAddress } from '../../lib/address.ts';

interface BookingListProps {
  bookings: Booking[];
  userRole: 'renter' | 'owner';
  onBookingAction?: (bookingId: string, action: string) => void;
  isLoading?: boolean;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  confirmed: 'bg-blue-100 text-blue-800 border-blue-200',
  paid: 'bg-green-100 text-green-800 border-green-200',
  completed: 'bg-slate-100 text-slate-800 border-slate-200',
  cancelled: 'bg-red-100 text-red-800 border-red-200',
  denied: 'bg-red-100 text-red-800 border-red-200',
};

const statusLabels = {
  pending: 'Pending Approval',
  confirmed: 'Confirmed - Payment Required',
  paid: 'Paid',
  completed: 'Completed',
  cancelled: 'Cancelled',
  denied: 'Denied',
};

export default function BookingList({ 
  bookings, 
  userRole, 
  onBookingAction,
  isLoading = false 
}: BookingListProps) {
  const [filter, setFilter] = useState<string>('all');

  const filteredBookings = bookings.filter(booking => {
    if (filter === 'all') return true;
    return booking.status === filter;
  });

  const handleAction = (bookingId: string, action: string) => {
    if (onBookingAction) {
      onBookingAction(bookingId, action);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getActionButtons = (booking: Booking) => {
    const buttons = [];

    // Owner actions for pending bookings
    if (userRole === 'owner' && booking.status === 'pending') {
      buttons.push(
        <Button
          key="approve"
          size="sm"
          className="bg-green-600 hover:bg-green-700 text-white"
          onClick={() => handleAction(booking.id, 'approve')}
        >
          <CheckCircle className="w-4 h-4 mr-1" />
          Approve
        </Button>
      );
      buttons.push(
        <Button
          key="deny"
          size="sm"
          variant="outline"
          className="border-red-300 text-red-600 hover:bg-red-50"
          onClick={() => handleAction(booking.id, 'deny')}
        >
          <XCircle className="w-4 h-4 mr-1" />
          Deny
        </Button>
      );
    }

    // Renter payment action
    if (userRole === 'renter' && booking.status === 'confirmed') {
      buttons.push(
        <Button
          key="pay"
          size="sm"
          className="bg-yellow-600 hover:bg-yellow-700 text-white"
          onClick={() => handleAction(booking.id, 'pay')}
        >
          <CreditCard className="w-4 h-4 mr-1" />
          Pay Now
        </Button>
      );
    }

    // Messaging for active bookings
    if (['confirmed', 'paid', 'completed'].includes(booking.status)) {
      buttons.push(
        <Button
          key="message"
          size="sm"
          variant="outline"
          onClick={() => handleAction(booking.id, 'message')}
        >
          <MessageSquare className="w-4 h-4 mr-1" />
          Message
        </Button>
      );
    }

    // View details (always available)
    buttons.push(
      <Button
        key="view"
        size="sm"
        variant="ghost"
        onClick={() => handleAction(booking.id, 'view')}
      >
        View Details
      </Button>
    );

    return buttons;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filter Tabs */}
      <div className="border-b border-slate-200">
        <nav className="-mb-px flex flex-wrap gap-x-8 gap-y-2">
          {['all', 'pending', 'confirmed', 'paid', 'completed', 'cancelled', 'denied'].map((status) => {
            const count = status === 'all'
              ? bookings.length
              : bookings.filter(b => b.status === status).length;

            return (
              <button
                key={status}
                type="button"
                onClick={() => setFilter(status)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  filter === status
                    ? 'border-primary-green text-primary-green'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                }`}
              >
                {status === 'all' ? 'All Bookings' : statusLabels[status as keyof typeof statusLabels]}
                <span className={`ml-2 py-0.5 px-2.5 rounded-full text-xs ${
                  filter === status
                    ? 'bg-primary-green bg-opacity-10 text-primary-green'
                    : 'bg-slate-100 text-slate-900'
                }`}>
                  {count}
                </span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Booking Cards */}
      {filteredBookings.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-slate-400 text-lg mb-2">No bookings found</div>
          <p className="text-slate-600">
            {filter === 'all'
              ? 'You don\'t have any bookings yet.'
              : `No ${filter} bookings at the moment.`
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredBookings.map((booking, index) => (
            <div
              key={`${booking.id}-${userRole}-${index}`}
              className="bg-white rounded-xl border border-slate-200 p-6 hover:shadow-card transition-all duration-300"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-semibold text-slate-900 mb-1">
                        {booking.venue.title}
                      </h3>
                      <div className="flex items-center text-slate-600 text-sm">
                        <MapPin className="w-4 h-4 mr-1" />
                        {formatAddress(booking.venue.address)}
                      </div>
                    </div>
                    <span
                      className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${
                        statusColors[booking.status as keyof typeof statusColors]
                      }`}
                    >
                      {statusLabels[booking.status as keyof typeof statusLabels]}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-slate-600">
                        <Calendar className="w-4 h-4 mr-2 text-slate-400" />
                        <div>
                          <div className="font-medium">Check-in</div>
                          <div>{formatDate(booking.start_date)}</div>
                        </div>
                      </div>
                      <div className="flex items-center text-sm text-slate-600">
                        <Calendar className="w-4 h-4 mr-2 text-slate-400" />
                        <div>
                          <div className="font-medium">Check-out</div>
                          <div>{formatDate(booking.end_date)}</div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-slate-600">
                        <Users className="w-4 h-4 mr-2 text-slate-400" />
                        <div>
                          <div className="font-medium">
                            {userRole === 'owner' ? 'Renter' : 'Venue Owner'}
                          </div>
                          <div>
                            {userRole === 'owner' ? booking.renter.name : booking.owner.name}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center text-sm text-slate-600">
                        <DollarSign className="w-4 h-4 mr-2 text-slate-400" />
                        <div>
                          <div className="font-medium">Total Amount</div>
                          <div className="text-lg font-bold text-primary-green">
                            {formatAmount(booking.total_price)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {booking.special_requests && (
                    <div className="bg-slate-50 rounded-lg p-3 mb-4">
                      <div className="text-sm font-medium text-slate-900 mb-1">Special Requests</div>
                      <div className="text-sm text-slate-600">{booking.special_requests}</div>
                    </div>
                  )}

                  {booking.venue.average_rating && (
                    <div className="mb-4">
                      <CompactStarRating
                        rating={booking.venue.average_rating}
                        reviewCount={booking.venue.review_count}
                      />
                    </div>
                  )}
                </div>

                <div className="flex flex-col gap-2 ml-6">
                  {getActionButtons(booking)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Plus, X, GripVertical } from "lucide-react"
import type { ChecklistItem } from "../../types/venue.ts"

interface ChecklistEditorProps {
  initialItems: ChecklistItem[]
  onChange: (items: ChecklistItem[]) => void
  disabled?: boolean
}

export default function ChecklistEditor({ initialItems, onChange, disabled }: ChecklistEditorProps) {
  const [items, setItems] = useState<ChecklistItem[]>(initialItems)
  const [newItemText, setNewItemText] = useState("")

  const updateItems = (newItems: ChecklistItem[]) => {
    setItems(newItems)
    onChange(newItems)
  }

  const addItem = () => {
    if (newItemText.trim()) {
      const newItem: ChecklistItem = {
        id: Date.now().toString(),
        text: newItemText.trim(),
        completed: false,
      }
      updateItems([...items, newItem])
      setNewItemText("")
    }
  }

  const removeItem = (id: string) => {
    updateItems(items.filter((item) => item.id !== id))
  }

  const updateItem = (id: string, updates: Partial<ChecklistItem>) => {
    updateItems(items.map((item) => (item.id === id ? { ...item, ...updates } : item)))
  }

  return (
    <div className="space-y-6">
      <div>
        <h3
          className="text-lg font-bold text-slate-900 mb-2"
          style={{ fontFamily: "'Poppins', 'Helvetica Neue', Arial, sans-serif" }}
        >
          Move-out Checklist
        </h3>
        <p className="text-sm text-slate-600" style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}>
          Create a checklist that renters must complete before leaving your venue
        </p>
      </div>

      {/* Add new item */}
      <div className="flex gap-3">
        <Input
          value={newItemText}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewItemText(e.target.value)}
          placeholder="Add a checklist item..."
          disabled={disabled}
          className="flex-1 px-4 py-3 border-2 border-slate-300 rounded-xl focus:border-emerald-500 focus:ring-4 focus:ring-emerald-500/20"
          onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => e.key === "Enter" && addItem()}
        />
        <Button
          type="button"
          onClick={addItem}
          disabled={disabled || !newItemText.trim()}
          className="px-4 py-3 bg-emerald-500 hover:bg-emerald-600 text-white rounded-xl transition-all duration-300 hover:scale-105"
        >
          <Plus className="w-4 h-4" />
        </Button>
      </div>

      {/* Checklist items */}
      <div className="space-y-3">
        {items.map((item, _index) => (
          <div
            key={item.id}
            className="flex items-center gap-3 p-4 bg-white border-2 border-slate-200 rounded-xl hover:border-slate-300 transition-all duration-300"
          >
            <GripVertical className="w-4 h-4 text-slate-400" />
            <Checkbox
              checked={item.completed}
              onCheckedChange={(checked: boolean) => updateItem(item.id, { completed: checked })}
              disabled={disabled}
              className="data-[state=checked]:bg-emerald-600 data-[state=checked]:border-emerald-600"
            />
            <Input
              value={item.text}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateItem(item.id, { text: e.target.value })}
              disabled={disabled}
              className="flex-1 border-0 focus:ring-0 p-0 text-sm"
            />
            <Button
              type="button"
              onClick={() => removeItem(item.id)}
              disabled={disabled}
              variant="ghost"
              size="sm"
              className="text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        ))}
      </div>

      {items.length === 0 && (
        <div className="text-center py-8 text-slate-500">
          <p>No checklist items yet. Add some items above to get started.</p>
        </div>
      )}
    </div>
  )
}
